export class IdleTimer {
    protected timeoutId?: NodeJS.Timeout

    public constructor(protected readonly timeout: number, protected readonly onTimeout: () => void) {}

    public start() {
        this.stop()
        this.timeoutId = setTimeout(() => this.onTimeout(), this.timeout)
    }

    public stop() {
        if (this.timeoutId) {
            clearTimeout(this.timeoutId)
            this.timeoutId = undefined
        }
    }

    public reset() {
        this.stop()
        this.start()
    }
}
