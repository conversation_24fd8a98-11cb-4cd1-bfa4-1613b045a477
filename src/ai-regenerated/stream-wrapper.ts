import type { ClientReadableStream, StatusObject } from '@grpc/grpc-js'
import { notNullish } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import type { Fn } from '@kdt310722/utils/function'
import { type Awaitable, type DeferredPromise, createDeferred } from '@kdt310722/utils/promise'
import { isCancelledGrpcStatus, isGrpcError, subscribeStream } from '../utils'

export enum StreamState {
    CLOSING = 'CLOSING',
    CLOSED = 'CLOSED',
    SUBSCRIBING = 'SUBSCRIBING',
    SUBSCRIBED = 'SUBSCRIBED',
}

export interface StreamWrapperTimeoutOptions {
    subscribe?: number
    close?: number
}

export interface StreamWrapperOptions {
    timeout?: StreamWrapperTimeoutOptions
}

export type StreamWrapperEvents<TData> = {
    error: (error: unknown) => void
    data: (data: TData) => void
    closed: (isExplicitly: boolean, error?: unknown) => void
}

export class StreamWrapper<TResponse, TStream extends ClientReadableStream<TResponse> = ClientReadableStream<TResponse>> extends Emitter<StreamWrapperEvents<TResponse>> {
    protected readonly closeTimeout: number
    protected readonly subscribeTimeout: number

    protected readonly streamListeners: Record<string, Fn> = {}

    protected stream?: TStream
    protected lastError?: unknown
    protected isExplicitlyClosed = false
    protected closePromise?: DeferredPromise<void>

    #state: StreamState = StreamState.CLOSED

    public constructor(protected readonly subscriber: (signal: AbortSignal) => Awaitable<TStream>, { timeout = {} }: StreamWrapperOptions = {}) {
        super()

        this.subscribeTimeout = timeout.subscribe ?? 10_000
        this.closeTimeout = timeout.close ?? this.subscribeTimeout
    }

    public get state() {
        return this.#state
    }

    public async subscribe(signal?: AbortSignal) {
        const state = this.state

        if (state === StreamState.SUBSCRIBED) {
            return
        }

        if (state !== StreamState.CLOSED) {
            throw new Error(`Stream is already in state: ${state}`)
        }

        this.setState(StreamState.SUBSCRIBING)
        this.stream = await subscribeStream(this.subscriber.bind(this), { signal, timeout: this.subscribeTimeout })

        const handlers = {
            data: this.handleData.bind(this, this.stream),
            error: this.handleError.bind(this, this.stream),
            status: this.handleStatus.bind(this, this.stream),
            close: this.handleClose.bind(this, this.stream),
        }

        for (const [event, handler] of Object.entries(handlers)) {
            this.stream.on(event, this.streamListeners[event] = handler)
        }

        this.setState(StreamState.SUBSCRIBED)
    }

    public async close(isExplicitly = true, destroyOnTimeout = true) {
        const state = this.state

        if (state === StreamState.CLOSED) {
            return
        }

        if (state !== StreamState.SUBSCRIBED) {
            throw new Error(`Stream is not in a state to close: ${state}`)
        }

        this.setState(StreamState.CLOSING)

        this.isExplicitlyClosed = isExplicitly
        this.closePromise = createDeferred<void>()

        const handleTimeout = () => {
            const error = new Error('Stream close timeout')

            if (destroyOnTimeout) {
                this.stream?.destroy(error)
            } else {
                this.closePromise?.reject(error)
            }
        }

        const timer = setTimeout(handleTimeout, this.closeTimeout)

        return this.closePromise.finally(() => {
            clearTimeout(timer)
            this.closePromise = undefined
        })
    }

    protected handleData(_stream: TStream, data: TResponse) {
        this.emitData(data)
    }

    protected emitData(data: TResponse) {
        this.emit('data', data)
    }

    protected handleStatus(stream: TStream, status: StatusObject) {
        this.handleClose(stream, status)
    }

    protected handleClose(_stream: TStream, status?: StatusObject) {
        const isExplicitly = this.isExplicitlyClosed
        const error = this.lastError ?? status

        this.reset()

        if (this.closePromise) {
            this.closePromise.resolve()
        }

        this.emit('closed', isExplicitly, error)

        if (!isExplicitly) {
            // TODO: Implement resubscribe logic if needed
        }
    }

    protected handleError(_stream: TStream, error: unknown) {
        if (isGrpcError(error) && isCancelledGrpcStatus(error)) {
            return
        }

        this.emit('error', this.lastError = error)
    }

    protected reset() {
        this.isExplicitlyClosed = false
        this.lastError = undefined

        if (notNullish(this.stream)) {
            for (const [event, handler] of Object.entries(this.streamListeners)) {
                this.stream.removeListener(event, handler)
            }

            this.stream = undefined
        }

        this.setState(StreamState.CLOSED)
    }

    protected setState(state: StreamState) {
        this.#state = state
    }
}
